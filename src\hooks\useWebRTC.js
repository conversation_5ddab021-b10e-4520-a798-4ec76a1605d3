import { ref } from 'vue'

export function useWebRTC() {
  const videoRef = ref(null)
  const audioRef = ref(null)
  const sessionId = ref('')
  const loading = ref(true)
  let pc = null

  // 清理所有媒体流
  const cleanupMediaStreams = () => {
    // 清理视频流
    if (videoRef.value && videoRef.value.srcObject) {
      const stream = videoRef.value.srcObject
      stream.getTracks().forEach((track) => track.stop())
      videoRef.value.srcObject = null
    }

    // 清理音频流
    if (audioRef.value && audioRef.value.srcObject) {
      const stream = audioRef.value.srcObject
      stream.getTracks().forEach((track) => track.stop())
      audioRef.value.srcObject = null
    }
  }
  // 清除当前正在播放的音频
  const clearAudio = () => {
    audioRef.value.pause()
    audioRef.value.currentTime = 0
  }

  // 完全清理连接
  const cleanup = () => {
    if (pc) {
      // 移除所有事件监听器
      pc.ontrack = null
      pc.onicecandidate = null
      pc.oniceconnectionstatechange = null
      pc.onsignalingstatechange = null
      pc.onicegatheringstatechange = null
      pc.onconnectionstatechange = null

      // 关闭连接
      pc.close()
      pc = null
    }

    // 清理媒体流
    cleanupMediaStreams()

    // 重置会话ID
    sessionId.value = ''
  }

  // 协商函数
  const negotiate = async () => {
    if (!pc) return

    try {
      // 添加音视频接收器
      pc.addTransceiver('video', { direction: 'recvonly' })
      pc.addTransceiver('audio', { direction: 'recvonly' })

      // 创建offer
      const offer = await pc.createOffer()
      await pc.setLocalDescription(offer)

      // 等待ICE收集完成
      await new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('ICE收集超时'))
        }, 10000) // 10秒超时

        if (pc.iceGatheringState === 'complete') {
          clearTimeout(timeout)
          resolve()
        } else {
          const checkState = () => {
            if (pc.iceGatheringState === 'complete') {
              pc.removeEventListener('icegatheringstatechange', checkState)
              clearTimeout(timeout)
              resolve()
            }
          }
          pc.addEventListener('icegatheringstatechange', checkState)
        }
      })

      // 发送offer到服务器
      const response = await fetch('http://*************:8010/offer', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          sdp: pc.localDescription?.sdp,
          type: pc.localDescription?.type
        })
      })

      if (!response.ok) {
        throw new Error(`服务器响应错误: ${response.status}`)
      }

      const answer = await response.json()
      if (!answer || !answer.sdp) {
        throw new Error('服务器返回的answer格式不正确')
      }

      sessionId.value = answer.sessionid
      await pc.setRemoteDescription(answer)
    } catch (error) {
      console.error('协商过程出错:', error)
      cleanup() // 出错时清理连接
      throw error
    }
  }

  // 开始连接
  const start = async (useStun = false) => {
    // 先清理之前的连接
    cleanup()

    const config = {
      sdpSemantics: 'unified-plan'
    }

    if (useStun) {
      config.iceServers = [{ urls: ['stun:stun.l.google.com:19302'] }]
    }

    pc = new RTCPeerConnection(config)

    // 监听连接状态
    pc.onconnectionstatechange = () => {
      console.log('连接状态:', pc?.connectionState)
      if (pc?.connectionState === 'connected') {
        loading.value = false
      }
      if (pc?.connectionState === 'failed' || pc?.connectionState === 'closed') {
        cleanup()
      }
    }

    // 监听ICE连接状态
    pc.oniceconnectionstatechange = () => {
      console.log('ICE连接状态:', pc?.iceConnectionState)
      if (pc?.iceConnectionState === 'failed') {
        cleanup()
      }
    }

    // 监听音视频轨道
    pc.ontrack = (evt) => {
      if (evt.track.kind === 'video') {
        videoRef.value.srcObject = evt.streams[0]
      } else {
        audioRef.value.srcObject = evt.streams[0]
      }
    }

    try {
      await negotiate()
    } catch (error) {
      cleanup()
      throw error
    }
  }

  // 停止连接
  const stop = () => {
    cleanup()
  }

  // 页面卸载时清理
  if (typeof window !== 'undefined') {
    window.addEventListener('beforeunload', cleanup)
    window.addEventListener('unload', cleanup)
  }

  async function sendMsg(text) {
    try {
      await fetch('http://*************:8010/human', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          sessionid: sessionId.value,
          type: 'echo',
          text: text
        })
      })
    } catch (error) {
      console.error('发送消息失败:', error)
    }
  }
  return {
    videoRef,
    audioRef,
    sessionId,
    loading,
    clearAudio,
    start,
    stop,
    sendMsg
  }
}
