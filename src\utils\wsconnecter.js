/**
 * Copyright FunASR (https://github.com/alibaba-damo-academy/FunASR). All Rights
 * Reserved. MIT License  (https://opensource.org/licenses/MIT)
 */
/* 2021-2023 by zhao<PERSON>,mali aihealthx.com */

import { useSocketStore } from '@/stores/ws'
import { useRecorder } from '@/hooks/useRecorder'
const hotwords = JSON.stringify({
  你好飞飞: 100
})
function WebSocketConnectMethod(url) {
  //定义socket连接方法类

  var speechSokt
  var reconnectAttempts = 0
  var temporaryText = ''
  var confirmedText = '' // 已确认的文字（不会被覆盖）
  const MAX_RECONNECT_ATTEMPTS = 5
  const RECONNECT_DELAY = 3000 // 3秒后重连

  this.wsStart = function () {
    if ('WebSocket' in window) {
      connect()
      return 1
    } else {
      alert('当前浏览器不支持 WebSocket')
      return 0
    }
  }

  const connect = () => {
    speechSokt = new WebSocket(url)
    speechSokt.onopen = onOpen
    speechSokt.onclose = onClose
    speechSokt.onmessage = onMessage
    speechSokt.onerror = onError
  }

  const reconnect = () => {
    console.log('reconnectAttempts', reconnectAttempts)
    if (reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
      reconnectAttempts++
      console.log(`尝试重连... (${reconnectAttempts}/${MAX_RECONNECT_ATTEMPTS})`)
      setTimeout(() => {
        connect()
      }, RECONNECT_DELAY)
    } else {
      useSocketStore().stateHandle(2) // 通知状态为错误
    }
  }

  // 定义停止与发送函数
  this.wsStop = function () {
    if (speechSokt != undefined) {
      console.log('stop ws!')
      speechSokt.close()
    }
  }

  this.wsSend = function (oneData) {
    if (speechSokt == undefined) return
    if (speechSokt.readyState === 1) {
      // 0:CONNECTING, 1:OPEN, 2:CLOSING, 3:CLOSED

      speechSokt.send(oneData)
    }
  }

  // 重置识别状态
  this.resetRecognition = function () {
    // 如果连接已打开
    if (speechSokt && speechSokt.readyState === 1) {
      // 发送结束当前识别会话的信号
      const endRequest = {
        is_speaking: false
      }
      speechSokt.send(JSON.stringify(endRequest))
      console.log('发送结束当前识别会话信号')

      // 短暂延迟后开始新的识别会话
      setTimeout(() => {
        // 开始新的识别会话
        const startRequest = {
          chunk_size: [5, 10, 5],
          hotwords: hotwords,
          wav_name: 'h5',
          is_speaking: true,
          chunk_interval: 10,
          itn: true,
          mode: '2pass-offline',
          audio_fs: 16000,
          audio_format: 'wav'
        }
        speechSokt.send(JSON.stringify(startRequest))
        console.log('发送开始新识别会话信号')
      }, 300) // 300ms延迟，确保结束信号被处理

      return true
    }
    return false
  }

  // SOCEKT连接中的消息与状态响应
  function onOpen() {
    reconnectAttempts = 0
    // 初始配置
    const request = {
      chunk_size: [5, 10, 5],
      hotwords: hotwords,
      wav_name: 'h5',
      is_speaking: true,
      chunk_interval: 10,
      itn: true,
      mode: '2pass-offline',
      audio_fs: 16000,
      audio_format: 'wav'
    }

    console.log('发送初始配置:', JSON.stringify(request, null, 2))
    speechSokt.send(JSON.stringify(request))
    console.log('连接成功')
    useSocketStore().stateHandle(1)
  }

  function onClose(e) {
    console.log('连接关闭!', e)
    useSocketStore().stateHandle(0)

    // 非主动关闭的情况下，尝试重连
    if (e.code !== 1000) {
      reconnect()
    }
  }

  function onMessage(e) {
    try {
      if (useRecorder().isRecording.value === '3') {
        return
      }

      const data = JSON.parse(e.data)
      // 处理语音识别结果
      // 设置状态state： 0未完成校验的文字 1:校验完成最终的文本
      if (data.stamp_sents) {
        // 校验完成的最终文本 - 将当前临时文本确认并追加到已确认文本
        confirmedText += data.text
        temporaryText = '' // 清空临时文本

        const messageData = {
          state: 1,
          text: confirmedText, // 发送完整的已确认文本
          isFinish: data.is_final
        }
        useSocketStore().msgHandle(messageData)
      } else {
        // 实时识别的临时文本
        temporaryText += data.text

        const messageData = {
          state: 0,
          text: confirmedText + temporaryText, // 已确认文本 + 当前临时文本
          isFinish: data.is_final
        }
        useSocketStore().msgHandle(messageData)
      }
    } catch (error) {
      console.error('解析消息失败：', error)
    }
  }

  function onError(e) {
    console.log('连接错误!', e)
    useSocketStore().stateHandle(2)
    // 发生错误时尝试重连
    reconnect()
  }
  this.resetMessage = function () {
    confirmedText = ''
    temporaryText = ''
  }
}
export default WebSocketConnectMethod
